# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-04 12:20:59 - Log of updates made.

*
      
## Decision

*
      
## Rationale 

*

## Implementation Details

*

---
### Decision (Code)
[2025-07-07 09:16:04] - Refactored `pointCheck` logic into `DataCache.CheckPoint()`

**Rationale:**
The original `pointCheck` logic was tightly coupled with the `FormMain` UI and performed direct database access, making it difficult to test and maintain. The refactoring was done to separate concerns, moving the business logic to a dedicated method in `DataCache` and removing the direct database dependency from the checking logic itself. This improves modularity and testability.

**Details:**
- Moved the point comparison logic from `FormMain.CheckPoint()` to `DataCache.CheckPoint()`.
- Modified `DataCache.CheckPoint()` to accept point data and a callback for updating the UI, removing its dependency on `FormMain` controls and `MySqlController`.
- Updated `FormMain.cs` to fetch data from `MySqlController` and pass it to the new `DataCache.CheckPoint()` method.
- Deleted the old `FormMain.CheckPoint()` method.
- Files affected: `DataCache.cs`, `FormMain.cs`

---
### Decision (Code)
[2025-07-07 10:24:09] - Conditionally execute point check database queries in `CheckResult`.

**Rationale:**
To optimize performance, the database queries to fetch left and right bracket points (`MySqlController.GetPoints`) and the subsequent call to `DataCache.CheckPoint` are now only executed if the `DataCache.pointCheck` flag is true. This prevents unnecessary database access when the point check is disabled, reducing execution time and database load.

**Details:**
- Wrapped the database query and `DataCache.CheckPoint` call within an `if (DataCache.pointCheck)` block in the `CheckResult` method.
- Files affected: `FormMain.cs`

---
### Decision (Code)
[2025-0

---
### Decision (Code)
[2025-07-07 10:33:39] - Fix point check logic by defaulting flags to true.

**Rationale:**
A logic flaw was identified where if the point check was disabled (`DataCache.pointCheck` is false), the associated boolean flags (`DataCache.centerX_Differ`, `DataCache.centerY_Differ`, etc.) were not being reset. This could cause the final result check to fail incorrectly based on stale data from a previous run. To fix this, all point check-related flags are now explicitly set to `true` before the conditional check, ensuring they default to a "pass" state when the check is skipped.

**Details:**
- Added a block of code in `FormMain.cs` within the `CheckResult` method to set ten `DataCache` point check flags to `true`.
- This block is placed immediately before the `if (DataCache.pointCheck)` statement.
- Files affected: `FormMain.cs`

---
### Decision (Code)
[2025-07-24 08:37:48] - Implemented `CheckSnCompliance` method in `FormMain.cs` to verify SN compliance.

**Rationale:**
A new requirement was introduced to check the compliance of a given SN by making a POST request to an external service. This method encapsulates the logic for making the HTTP request, handling the response, and performing error logging, ensuring that the SN check is modular and reusable. The method is implemented asynchronously to prevent blocking the UI thread during the network call.

**Details:**
- Added a new `private async Task<bool> CheckSnCompliance(string sn, string resCode, out string serialNo)` method to `FormMain.cs`.
- The method uses `HttpClient` to send a POST request to `http://172.20.96.17:8888/sn/check`.
- It serializes the request body to JSON and deserializes the JSON response using `System.Text.Json`.
- Error handling for `HttpRequestException`, `JsonException`, and other potential exceptions is included.
- Added `using System.Net.Http;` to `FormMain.cs`.
- Files affected: `FormMain.cs`

---
### Decision (Code)
[2025-07-29 11:10:14] - Made `CheckSnCompliance` calls conditional.

**Rationale:**
The `CheckSnCompliance` check should only be performed when MES is enabled. This change prevents unnecessary external API calls when the system is not configured to interact with MES, improving performance and avoiding potential errors in offline or testing scenarios.

**Details:**
- Wrapped the two `CheckSnCompliance(beam_sn)` calls in `FormMain.cs` within an `if (LOGINFO.mesEnable == "true")` block.
- Files affected: `FormMain.cs`

---
### Decision (Code)
[2025-07-29 11:29:31] - Replaced hardcoded "resCode" with `DataCache.MesResource`

**Rationale:**
The `CheckSnCompliance` method was using a hardcoded string "resCode" as a parameter. To improve maintainability and centralization of configuration, this was replaced with `DataCache.MesResource`. This ensures that the resource code is managed in a single location within the `DataCache`, making future updates easier and reducing the risk of inconsistencies.

**Details:**
- Modified two calls to `CheckSnCompliance` in `FormMain.cs` to use `DataCache.MesResource`.
- Files affected: `FormMain.cs`

---
### Decision (Code)
[2025-07-29 03:37:26] - Refactored `CheckSnCompliance` to resolve CS1988 error.

**Rationale:**
The original `async` method `CheckSnCompliance` used an `out` parameter, which is not allowed and caused a compile-time error (CS1988). To fix this, the method was refactored to return a value tuple `(bool Success, string SerialNo)` instead. This approach eliminates the `out` parameter while still providing both the success status and the resulting serial number to the caller in a type-safe and readable manner.

**Details:**
- Changed the method signature of `CheckSnCompliance` in `FormMain.cs` from `private async Task<bool> CheckSnCompliance(string sn, string resCode, out string serialNo)` to `private async Task<(bool Success, string SerialNo)> CheckSnCompliance(string sn, string resCode)`.
- Updated the return statements to provide a tuple, e.g., `return (true, serialNo);`.
- Modified the two call sites in `test_Execute` and `TB_FixBeam_KeyUp` to `await` the asynchronous call and destructure the returned tuple to get the results.
- Marked `TB_FixBeam_KeyUp` as `async void` to support the `await` call.
- Files affected: `FormMain.cs`

---
### Decision (Code)
[2025-07-29 11:46:00] - 在 `InsertDataOld` 方法中添加 `serialNo` 参数以增强数据追踪能力

**Rationale:**
根据用户需求，需要将 `_currentSerialNo` 的值存入数据库的 `serialNo` 字段。为此，我修改了 `MySqlController.cs` 中的 `InsertDataOld` 方法，增加了一个新的字符串参数 `serialNo`，并相应地更新了 `INSERT` SQL 语句。同时，我也更新了 `FormMain.cs` 中对 `InsertDataOld` 方法的调用，以传递此新参数。

**Details:**
- **`MySqlController.cs`**: `InsertDataOld` 方法签名已更新，包含了新的 `serialNo` 参数和相应的SQL插入逻辑。
- **`FormMain.cs`**: 在 `sqlExcute` 方法中，对 `MySqlController.InsertDataOld` 的调用已更新，传递了 `_currentSerialNo` 变量。

---
### Decision (Code)
[2025-07-29 12:16:10] - 在 `CleanAll()` 方法中添加 `_currentSerialNo` 的清空逻辑。

**Rationale:**
分析发现 `_currentSerialNo` 变量在新的测试流程开始时没有被重置。这可能导致在MES服务调用失败或未获取新序列号的情况下，上一个测试的序列号被错误地重用到下一个测试中，造成数据记录不一致。通过在 `CleanAll()` 方法中将 `_currentSerialNo` 设置为空字符串，可以确保每次清理操作（如开始新测试、切换模式等）都会重置该变量，从而防止数据污染，保证了数据持久化的准确性。

**Details:**
- 在 `FormMain.cs` 的 `CleanAll()` 方法的开头，增加了 `_currentSerialNo = "";` 这行代码。
- Files affected: `FormMain.cs`

---
### Decision (Code)
[2025-07-29 08:08:20] - 在 `CheckSnCompliance` 方法中添加了详细的结果日志

**Rationale:**
为了便于调试和追踪 `CheckSnCompliance` 方法的执行结果，需要将该方法的返回值（`Success` 状态和 `SerialNo`）记录到日志中。通过在方法返回前添加一条 `Logs.WriteInfo` 日志，可以清晰地看到每次调用的输出，而无需附加调试器。同时，对方法进行了轻微重构，以确保在所有执行路径（成功或失败）的末尾都有一个统一的返回点，从而使日志记录更加一致和可靠。

**Details:**
- 在 `FormMain.cs` 的 `CheckSnCompliance` 方法中，引入了一个本地元组变量 `result` 来存储最终的返回状态。
- 在方法的末尾，添加了一行 `Logs.WriteInfo` 调用，以记录 `result.Success` 和 `result.SerialNo` 的值。
- 修改了方法内的返回逻辑，使其在所有分支中都更新 `result` 变量，并在方法末尾统一返回 `result`。
- Files affected: `FormMain.cs`