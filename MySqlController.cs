using ParamManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Management.Instrumentation;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;


namespace DoubleCamPreliminary
{
    public class MySqlController
    {
        public static bool connInited;

        public static int GetTestValue(string sn,out double mtf,out double gray)
        {
            mtf = 0;
            gray = 0;

            //if (!connInited)
            //    return -1;


            //查询新表
            bool haveData;
            string searchStr = "SELECT grayAvg AS gray,mtf2PixelAvg AS mtf FROM g_single_cam WHERE BracketSN='" + sn + "' ORDER BY Time DESC";

            byte[] result = new byte[1024];
            int len = result.Length;
            DataSet dataResult = new DataSet();
            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            Logs.WriteDebug("ret " +ret.ToString(),true);
            if  (ret != 0)
            {
                MessageBox.Show("数据库查询失败");
                return ret;
            }

            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                mtf = Convert.ToDouble(dataResult.Tables[0].Rows[0]["mtf"]);
                gray = Convert.ToDouble(dataResult.Tables[0].Rows[0]["gray"]);
                //haveData = true;
                return 0;
            }
            else
            {
                //haveData = false;
                return -1;
            }

            /*
            //查询老表
            if (!haveData)
            {
                //adapter.Dispose();
                dataResult.Clear();
                searchStr = "SELECT GrayAverage AS gray,MTF2PixelWhiteAverage AS mtf FROM singlecam WHERE SN='" + sn + "' ORDER BY Time DESC";

                result = new byte[1024];
                len = result.Length;
                XrPLCom.getDbData(Encoding.UTF8.GetBytes(searchStr), result, ref len);
                if (ret != 0)
                {
                    MessageBox.Show("数据库查询失败");
                }
                content = Encoding.UTF8.GetString(result, 0, len);
                dataResult = Newtonsoft.Json.JsonConvert.DeserializeObject<DataSet>(content);

                //adapter = new MySqlDataAdapter(searchStr, conn);
                //adapter.Fill(dataResult);
                if (dataResult.Tables[0].Rows.Count >= 1)
                {
                    mtf = Convert.ToDouble(dataResult.Tables[0].Rows[0]["mtf"]);
                    gray = Convert.ToDouble(dataResult.Tables[0].Rows[0]["gray"]);             
                }
                else
                {
                    return -2;
                }

            }
            */

            return 0;
        }

        public static int InsertDataOld(string sn, string station, string result, string leftBracketSn, string LeftoledSn,
            string rightBracketSn, string rightOledSn, string serialNo)
        {
            sn = sn.ToUpper();
            leftBracketSn = leftBracketSn.ToUpper();
            rightBracketSn = rightBracketSn.ToUpper();
            LeftoledSn = LeftoledSn.ToUpper();
            rightOledSn = rightOledSn.ToUpper();
            string insertStr = "INSERT INTO g_dulecup_pre_check (SN,`LeftBracketSN`,`RightBracketSN`,`LeftOledSN`,`RightOledSN`,Result,`Station`,`User`, `serialNo`) VALUES(" +
                "'" + sn +
                "','" + leftBracketSn +
                "','" + rightBracketSn +
                "','" + LeftoledSn +
                "','" + rightOledSn +
                "','" + result +
                "','" + station +
                "','" + LOGINFO.user +
                "','" + serialNo +
                "')";

            int res = XrPLCom.excuteGinaDb(LOGINFO.mode, LOGINFO.dbWrite, insertStr);
            if (res != 0)
            {
                MessageBox.Show("数据库写入失败");
                return -1;
            }

            return 0;
        }

        public static int CheckFeature(string sn, string featureCode)
        {
            string searchStr = "select OledFeatureCode from g_oled_feature where OledSN='" + sn + "'";

            DataSet dataResult = new DataSet();
            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret != 0)
            {
                MessageBox.Show("数据库查询失败");
                return ret;
            }

            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                string featureCodeTrue = dataResult.Tables[0].Rows[0]["OledFeatureCode"].ToString();
                if (featureCodeTrue != featureCode)
                    return 2;
                else
                    return 0;
            }
            else
            {
                return 1;
            }
        }

        public static int GetOledSn(string mainSn, out string leftOled, out string rightOled)
        {
            leftOled = "";
            rightOled = "";
            string searchStr = "SELECT `LeftOledSN` AS LeftOled,`RightOledSN` AS RightOled FROM g_dulecup_pre_check WHERE SN='" + mainSn + "' ORDER BY Time DESC";

            DataSet dataResult = new DataSet();
            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret != 0)
            {
                Logs.WriteDebug("GetGDbData error " + ret, true);
                MessageBox.Show("读取Oled失败");
                return ret;
            }

            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                leftOled = dataResult.Tables[0].Rows[0]["LeftOled"].ToString();
                rightOled = dataResult.Tables[0].Rows[0]["RightOled"].ToString();
                return 0;
            }
            else
            {
                return -1;
            }

        }

        public static int GetAngle2(string oledsn, out double angel2)
        {
            angel2 = 0.0;
            string searchStr = "SELECT angel2 FROM g_single_cam WHERE OledSN='" + oledsn + "' ORDER BY Time DESC";

            DataSet dataResult = new DataSet();
            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret != 0)
            {
                Logs.WriteDebug("GetGDbData error " + ret, true);
                MessageBox.Show("读取angel2失败");
                return ret;
            }
            //MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, conn);
            //DataSet dataResult = new DataSet();
            //adapter.Fill(dataResult);

            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                try
                {
                    angel2 = double.Parse(dataResult.Tables[0].Rows[0]["angel2"].ToString());
                }
                catch (Exception)
                {
                    return -1;
                }

                return 0;
            }
            else
            {
                return -1;
            }
        }

        public static int GetDiopter(string BracketSn, out double diopter)
        {
            diopter = 0.0;
            string searchStr = "";
            string side = BracketSn.Substring(4, 1);
            if (side == "L")
            {
                searchStr = "SELECT LeftDiopter FROM g_vid WHERE LeftBracketSn='" + BracketSn + "' ORDER BY Time DESC";
            }
            else if (side == "R")
            {
                searchStr = "SELECT RightDiopter FROM station_vid WHERE RightBracketSn='" + BracketSn + "' ORDER BY Time DESC";
            }
            else
                return -1;

            DataSet dataResult = new DataSet();
            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret != 0)
            {
                Logs.WriteDebug("GetGDbData error " + ret, true);
                MessageBox.Show("读取Diopter失败");
                return ret;
            }
            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                try
                {
                    if (side == "L")
                    {
                        diopter = double.Parse(dataResult.Tables[0].Rows[0]["LeftDiopter"].ToString());
                    }
                    else
                    {
                        diopter = double.Parse(dataResult.Tables[0].Rows[0]["RightDiopter"].ToString());
                    }
                }
                catch (Exception)
                {
                    return -1;
                }

                return 0;
            }
            else
            {
                return -1;
            }
        }

        public static int GetLeftOledSnByBracketSn(string leftBracketSn, out string leftOled, out string beamsn)
        {
            leftOled = "";
            beamsn = "";
            string searchStr = "SELECT `LeftOledSN` AS LeftOled,SN FROM g_dulecup_pre_check WHERE `LeftBracketSN`='" + leftBracketSn + "' ORDER BY Time DESC";

            DataSet dataResult = new DataSet();
            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret != 0)
            {
                Logs.WriteDebug("GetGDbData error " + ret, true);
                MessageBox.Show("读取LeftOled失败");
                return ret;
            }

            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                leftOled = dataResult.Tables[0].Rows[0]["LeftOled"].ToString();
                beamsn = dataResult.Tables[0].Rows[0]["SN"].ToString();
                return 0;
            }
            else
            {
                return -1;
            }

        }

        public static int GetRightOledSnByBracketSn(string rightBracketSn, out string rightOled, out string beamsn)
        {
            rightOled = "";
            beamsn = "";
            string searchStr = "SELECT `RightOledSN` AS RightOled,SN FROM g_dulecup_pre_check WHERE `RightBracketSN`='" + rightBracketSn + "' ORDER BY Time DESC";

            DataSet dataResult = new DataSet();
            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret != 0)
            {
                Logs.WriteDebug("GetGDbData error " + ret, true);
                MessageBox.Show("读取RightOled失败");
                return ret;
            }

            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                rightOled = dataResult.Tables[0].Rows[0]["RightOled"].ToString();
                beamsn = dataResult.Tables[0].Rows[0]["SN"].ToString();
                return 0;
            }
            else
            {
                return -1;
            }

        }

        public static int GetMagRates(string bracketSn, out double magLR, out double magTB)
        {
            magLR = 0.0;
            magTB = 0.0;

            string searchStr = "SELECT magLeftRight, magTopBot FROM g_single_cam WHERE BracketSN='" + bracketSn + "' ORDER BY Time DESC";
            DataSet dataResult = new DataSet();
            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret != 0)
            {
                Logs.WriteDebug("GetGDbData error " + ret, true);
                MessageBox.Show("读取放大率失败");
                return ret;
            }

            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                try
                {
                    magLR = double.Parse(dataResult.Tables[0].Rows[0]["magLeftRight"].ToString());
                    magTB = double.Parse(dataResult.Tables[0].Rows[0]["magTopBot"].ToString());
                }
                catch (Exception ex)
                {
                    Logs.WriteError("Error parsing mag rates: " + ex.Message, true);
                    return -1;
                }

                return 0;
            }
            else
            {
                return -1;
            }
        }

        public static int GetmagLR(string BracketSn, out double mag)
        {
            mag = 0.0;
            string searchStr = "SELECT magLeftRight FROM g_single_cam WHERE BracketSN='" + BracketSn + "' ORDER BY Time DESC";
            DataSet dataResult = new DataSet();
            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret != 0)
            {
                Logs.WriteDebug("GetGDbData error " + ret, true);
                MessageBox.Show("读取左右放大率失败");
                return ret;
            }

            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                try
                {
                    mag = double.Parse(dataResult.Tables[0].Rows[0]["magLeftRight"].ToString());
                }
                catch (Exception)
                {
                    return -1;
                }

                return 0;
            }
            else
            {
                return -1;
            }
        }

        public static int GetmagTB(string BracketSn, out double mag)
        {
            mag = 0.0;
            string searchStr = "SELECT magTopBot FROM g_single_cam WHERE BracketSN='" + BracketSn + "' ORDER BY Time DESC";
            DataSet dataResult = new DataSet();
            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret != 0)
            {
                Logs.WriteDebug("GetGDbData error " + ret, true);
                MessageBox.Show("读取上下放大率失败");
                return ret;
            }

            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                try
                {
                    mag = double.Parse(dataResult.Tables[0].Rows[0]["magTopBot"].ToString());
                }
                catch (Exception)
                {
                    return -1;
                }

                return 0;
            }
            else
            {
                return -1;
            }
        }

        public static int GetPoints(string bracketSn, out Dictionary<string, double> points)
        {
            points = new Dictionary<string, double>();

            string searchStr = "SELECT Circle12_OffsetX, Circle12_OffsetY, Circle0_OffsetX, Circle0_OffsetY, " +
                              "Circle4_OffsetX, Circle4_OffsetY, Circle20_OffsetX, Circle20_OffsetY, " +
                              "Circle24_OffsetX, Circle24_OffsetY FROM g_singlecam_circleoffset WHERE SN='" + bracketSn + "' ORDER BY Time DESC";

            DataSet dataResult = new DataSet();
            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret != 0)
            {
                Logs.WriteDebug("GetGDbData error " + ret, true);
                return ret;
            }

            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                try
                {
                    DataRow row = dataResult.Tables[0].Rows[0];
                    points["centerX"] = double.Parse(row["Circle12_OffsetX"].ToString());
                    points["centerY"] = double.Parse(row["Circle12_OffsetY"].ToString());
                    points["point0X"] = double.Parse(row["Circle0_OffsetX"].ToString());
                    points["point0Y"] = double.Parse(row["Circle0_OffsetY"].ToString());
                    points["point4X"] = double.Parse(row["Circle4_OffsetX"].ToString());
                    points["point4Y"] = double.Parse(row["Circle4_OffsetY"].ToString());
                    points["point20X"] = double.Parse(row["Circle20_OffsetX"].ToString());
                    points["point20Y"] = double.Parse(row["Circle20_OffsetY"].ToString());
                    points["point24X"] = double.Parse(row["Circle24_OffsetX"].ToString());
                    points["point24Y"] = double.Parse(row["Circle24_OffsetY"].ToString());
                }
                catch (Exception ex)
                {
                    Logs.WriteError("Error parsing points data: " + ex.Message, true);
                    return -1;
                }
                return 0;
            }
            else
            {
                return -1;
            }
        }

        public static int GetPoint(string BracketSn, string pointName, out double pointValue)
        {
            pointValue = 0.0;

            // Map parameter name to database column name
            string columnName = "";
            switch (pointName)
            {
                case "centerX":
                    columnName = "Circle12_OffsetX";
                    break;
                case "centerY":
                    columnName = "Circle12_OffsetY";
                    break;
                case "point0X":
                    columnName = "Circle0_OffsetX";
                    break;
                case "point0Y":
                    columnName = "Circle0_OffsetY";
                    break;
                case "point4X":
                    columnName = "Circle4_OffsetX";
                    break;
                case "point4Y":
                    columnName = "Circle4_OffsetY";
                    break;
                case "point20X":
                    columnName = "Circle20_OffsetX";
                    break;
                case "point20Y":
                    columnName = "Circle20_OffsetY";
                    break;
                case "point24X":
                    columnName = "Circle24_OffsetX";
                    break;
                case "point24Y":
                    columnName = "Circle24_OffsetY";
                    break;
                default:
                    Logs.WriteError("Invalid point parameter name: " + pointName, true);
                    return -1;
            }

            string searchStr = "SELECT " + columnName + " FROM g_singlecam_circleoffset WHERE SN='" + BracketSn + "' ORDER BY Time DESC";
            DataSet dataResult = new DataSet();

            int ret = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret != 0)
            {
                Logs.WriteDebug("GetGDbData error " + ret, true);
                //MessageBox.Show("读取" + pointName + "失败");
                return ret;
            }

            if (dataResult.Tables[0].Rows.Count >= 1)
            {
                try
                {
                    pointValue = double.Parse(dataResult.Tables[0].Rows[0][columnName].ToString());
                }
                catch (Exception ex)
                {
                    Logs.WriteError("Error parsing " + pointName + ": " + ex.Message, true);
                    return -1;
                }
                return 0;
            }
            else
            {
                return -1;
            }
        }

    }
}
