﻿using System;

namespace DoubleCamPreliminary
{
    partial class FormMain
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormMain));
            this.Group_Sn = new System.Windows.Forms.GroupBox();
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.mtf = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gray = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.angle = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.lr_magrate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.tb_magrate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.center_x = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.center_y = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.point0_x = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.point0_y = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.point4_x = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.point4_y = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.point20_x = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.point20_y = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.point24_x = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.point24_y = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.beam_sn_label = new System.Windows.Forms.Label();
            this.Btn_CleanRight = new System.Windows.Forms.Button();
            this.Btn_CleanLeft = new System.Windows.Forms.Button();
            this.TB_RightOledSn = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.TB_RightBracketSn = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.TB_LeftOledSn = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.TB_LeftBracketSn = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.Label_BeamSn = new System.Windows.Forms.Label();
            this.GB_1 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.TB_Code_3 = new System.Windows.Forms.TextBox();
            this.CB_Reject_3 = new System.Windows.Forms.ComboBox();
            this.CB_Position_3 = new System.Windows.Forms.ComboBox();
            this.TB_Code_2 = new System.Windows.Forms.TextBox();
            this.CB_Reject_2 = new System.Windows.Forms.ComboBox();
            this.CB_Position_2 = new System.Windows.Forms.ComboBox();
            this.TB_Code_1 = new System.Windows.Forms.TextBox();
            this.CB_Reject_1 = new System.Windows.Forms.ComboBox();
            this.CB_Position_1 = new System.Windows.Forms.ComboBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.TB_Remark = new System.Windows.Forms.TextBox();
            this.Btn_OK = new System.Windows.Forms.Button();
            this.Btn_NG = new System.Windows.Forms.Button();
            this.Label_Tip = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.tb_FixBeam = new System.Windows.Forms.TextBox();
            this.radioButton4 = new System.Windows.Forms.RadioButton();
            this.radioButton3 = new System.Windows.Forms.RadioButton();
            this.TB_FixSN = new System.Windows.Forms.TextBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.radioButton2 = new System.Windows.Forms.RadioButton();
            this.radioButton1 = new System.Windows.Forms.RadioButton();
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.菜单ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.参数配置ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Group_Sn.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.GB_1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.menuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // Group_Sn
            // 
            this.Group_Sn.Controls.Add(this.dataGridView1);
            this.Group_Sn.Controls.Add(this.beam_sn_label);
            this.Group_Sn.Controls.Add(this.Btn_CleanRight);
            this.Group_Sn.Controls.Add(this.Btn_CleanLeft);
            this.Group_Sn.Controls.Add(this.TB_RightOledSn);
            this.Group_Sn.Controls.Add(this.label5);
            this.Group_Sn.Controls.Add(this.TB_RightBracketSn);
            this.Group_Sn.Controls.Add(this.label4);
            this.Group_Sn.Controls.Add(this.TB_LeftOledSn);
            this.Group_Sn.Controls.Add(this.label3);
            this.Group_Sn.Controls.Add(this.TB_LeftBracketSn);
            this.Group_Sn.Controls.Add(this.label2);
            this.Group_Sn.Controls.Add(this.Label_BeamSn);
            this.Group_Sn.Location = new System.Drawing.Point(24, 97);
            this.Group_Sn.Name = "Group_Sn";
            this.Group_Sn.Size = new System.Drawing.Size(811, 714);
            this.Group_Sn.TabIndex = 0;
            this.Group_Sn.TabStop = false;
            this.Group_Sn.Text = "SN扫描";
            // 
            // dataGridView1
            // 
            this.dataGridView1.BackgroundColor = System.Drawing.SystemColors.Menu;
            this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.mtf,
            this.gray,
            this.angle,
            this.lr_magrate,
            this.tb_magrate,
            this.center_x,
            this.center_y,
            this.point0_x,
            this.point0_y,
            this.point4_x,
            this.point4_y,
            this.point20_x,
            this.point20_y,
            this.point24_x,
            this.point24_y});
            this.dataGridView1.Location = new System.Drawing.Point(25, 392);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.RowTemplate.Height = 30;
            this.dataGridView1.Size = new System.Drawing.Size(756, 215);
            this.dataGridView1.TabIndex = 30;
            // 
            // mtf
            // 
            this.mtf.HeaderText = "MTF";
            this.mtf.Name = "mtf";
            this.mtf.ReadOnly = true;
            // 
            // gray
            // 
            this.gray.HeaderText = "灰度";
            this.gray.Name = "gray";
            this.gray.ReadOnly = true;
            // 
            // angle
            // 
            this.angle.HeaderText = "斜率";
            this.angle.Name = "angle";
            this.angle.ReadOnly = true;
            // 
            // lr_magrate
            // 
            this.lr_magrate.HeaderText = "左右放大率";
            this.lr_magrate.Name = "lr_magrate";
            this.lr_magrate.ReadOnly = true;
            // 
            // tb_magrate
            // 
            this.tb_magrate.HeaderText = "上下放大率";
            this.tb_magrate.Name = "tb_magrate";
            this.tb_magrate.ReadOnly = true;
            // 
            // center_x
            // 
            this.center_x.HeaderText = "中心X";
            this.center_x.Name = "center_x";
            this.center_x.ReadOnly = true;
            // 
            // center_y
            // 
            this.center_y.HeaderText = "中心Y";
            this.center_y.Name = "center_y";
            this.center_y.ReadOnly = true;
            // 
            // point0_x
            // 
            this.point0_x.HeaderText = "点0X";
            this.point0_x.Name = "point0_x";
            this.point0_x.ReadOnly = true;
            // 
            // point0_y
            // 
            this.point0_y.HeaderText = "点0Y";
            this.point0_y.Name = "point0_y";
            this.point0_y.ReadOnly = true;
            // 
            // point4_x
            // 
            this.point4_x.HeaderText = "点4X";
            this.point4_x.Name = "point4_x";
            this.point4_x.ReadOnly = true;
            // 
            // point4_y
            // 
            this.point4_y.HeaderText = "点4Y";
            this.point4_y.Name = "point4_y";
            this.point4_y.ReadOnly = true;
            // 
            // point20_x
            // 
            this.point20_x.HeaderText = "点20X";
            this.point20_x.Name = "point20_x";
            this.point20_x.ReadOnly = true;
            // 
            // point20_y
            // 
            this.point20_y.HeaderText = "点20Y";
            this.point20_y.Name = "point20_y";
            this.point20_y.ReadOnly = true;
            // 
            // point24_x
            // 
            this.point24_x.HeaderText = "点24X";
            this.point24_x.Name = "point24_x";
            this.point24_x.ReadOnly = true;
            // 
            // point24_y
            // 
            this.point24_y.HeaderText = "点24Y";
            this.point24_y.Name = "point24_y";
            // 
            // beam_sn_label
            // 
            this.beam_sn_label.AutoSize = true;
            this.beam_sn_label.Location = new System.Drawing.Point(186, 30);
            this.beam_sn_label.Name = "beam_sn_label";
            this.beam_sn_label.Size = new System.Drawing.Size(0, 27);
            this.beam_sn_label.TabIndex = 29;
            // 
            // Btn_CleanRight
            // 
            this.Btn_CleanRight.BackColor = System.Drawing.Color.Khaki;
            this.Btn_CleanRight.FlatAppearance.MouseDownBackColor = System.Drawing.Color.DarkOrange;
            this.Btn_CleanRight.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Coral;
            this.Btn_CleanRight.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.Btn_CleanRight.Location = new System.Drawing.Point(393, 628);
            this.Btn_CleanRight.Name = "Btn_CleanRight";
            this.Btn_CleanRight.Size = new System.Drawing.Size(200, 80);
            this.Btn_CleanRight.TabIndex = 26;
            this.Btn_CleanRight.Text = "清空右目";
            this.Btn_CleanRight.UseVisualStyleBackColor = false;
            this.Btn_CleanRight.Click += new System.EventHandler(this.Btn_CleanRight_Click);
            // 
            // Btn_CleanLeft
            // 
            this.Btn_CleanLeft.BackColor = System.Drawing.Color.Khaki;
            this.Btn_CleanLeft.FlatAppearance.MouseDownBackColor = System.Drawing.Color.DarkOrange;
            this.Btn_CleanLeft.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Coral;
            this.Btn_CleanLeft.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.Btn_CleanLeft.Location = new System.Drawing.Point(65, 628);
            this.Btn_CleanLeft.Name = "Btn_CleanLeft";
            this.Btn_CleanLeft.Size = new System.Drawing.Size(200, 80);
            this.Btn_CleanLeft.TabIndex = 25;
            this.Btn_CleanLeft.Text = "清空左目";
            this.Btn_CleanLeft.UseVisualStyleBackColor = false;
            this.Btn_CleanLeft.Click += new System.EventHandler(this.Btn_CleanLeft_Click);
            // 
            // TB_RightOledSn
            // 
            this.TB_RightOledSn.ImeMode = System.Windows.Forms.ImeMode.Disable;
            this.TB_RightOledSn.Location = new System.Drawing.Point(147, 295);
            this.TB_RightOledSn.Name = "TB_RightOledSn";
            this.TB_RightOledSn.Size = new System.Drawing.Size(464, 34);
            this.TB_RightOledSn.TabIndex = 11;
            this.TB_RightOledSn.Enter += new System.EventHandler(this.TB_RightOledSn_Enter);
            this.TB_RightOledSn.KeyUp += new System.Windows.Forms.KeyEventHandler(this.TB_RightOledSn_KeyUp);
            this.TB_RightOledSn.Leave += new System.EventHandler(this.TB_RightOledSn_Leave);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(60, 298);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(81, 27);
            this.label5.TabIndex = 10;
            this.label5.Text = "右屏SN";
            // 
            // TB_RightBracketSn
            // 
            this.TB_RightBracketSn.ImeMode = System.Windows.Forms.ImeMode.Disable;
            this.TB_RightBracketSn.Location = new System.Drawing.Point(147, 228);
            this.TB_RightBracketSn.Name = "TB_RightBracketSn";
            this.TB_RightBracketSn.Size = new System.Drawing.Size(464, 34);
            this.TB_RightBracketSn.TabIndex = 9;
            this.TB_RightBracketSn.Enter += new System.EventHandler(this.TB_RightBracketSn_Enter);
            this.TB_RightBracketSn.KeyUp += new System.Windows.Forms.KeyEventHandler(this.TB_RightBracketSn_KeyUp);
            this.TB_RightBracketSn.Leave += new System.EventHandler(this.TB_RightBracketSn_Leave);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(40, 231);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(101, 27);
            this.label4.TabIndex = 8;
            this.label4.Text = "右套筒SN";
            // 
            // TB_LeftOledSn
            // 
            this.TB_LeftOledSn.ImeMode = System.Windows.Forms.ImeMode.Disable;
            this.TB_LeftOledSn.Location = new System.Drawing.Point(147, 161);
            this.TB_LeftOledSn.Name = "TB_LeftOledSn";
            this.TB_LeftOledSn.Size = new System.Drawing.Size(464, 34);
            this.TB_LeftOledSn.TabIndex = 7;
            this.TB_LeftOledSn.Enter += new System.EventHandler(this.TB_LeftOledSn_Enter);
            this.TB_LeftOledSn.KeyUp += new System.Windows.Forms.KeyEventHandler(this.TB_LeftOledSn_KeyUp);
            this.TB_LeftOledSn.Leave += new System.EventHandler(this.TB_LeftOledSn_Leave);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(60, 164);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(81, 27);
            this.label3.TabIndex = 6;
            this.label3.Text = "左屏SN";
            // 
            // TB_LeftBracketSn
            // 
            this.TB_LeftBracketSn.ImeMode = System.Windows.Forms.ImeMode.Disable;
            this.TB_LeftBracketSn.Location = new System.Drawing.Point(147, 94);
            this.TB_LeftBracketSn.Name = "TB_LeftBracketSn";
            this.TB_LeftBracketSn.Size = new System.Drawing.Size(464, 34);
            this.TB_LeftBracketSn.TabIndex = 5;
            this.TB_LeftBracketSn.Enter += new System.EventHandler(this.TB_LeftBracketSn_Enter);
            this.TB_LeftBracketSn.KeyUp += new System.Windows.Forms.KeyEventHandler(this.TB_LeftBracketSn_KeyUp);
            this.TB_LeftBracketSn.Leave += new System.EventHandler(this.TB_LeftBracketSn_Leave);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(40, 97);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(101, 27);
            this.label2.TabIndex = 4;
            this.label2.Text = "左套筒SN";
            // 
            // Label_BeamSn
            // 
            this.Label_BeamSn.AutoSize = true;
            this.Label_BeamSn.Location = new System.Drawing.Point(60, 30);
            this.Label_BeamSn.Name = "Label_BeamSn";
            this.Label_BeamSn.Size = new System.Drawing.Size(81, 27);
            this.Label_BeamSn.TabIndex = 2;
            this.Label_BeamSn.Text = "横梁SN";
            // 
            // GB_1
            // 
            this.GB_1.Controls.Add(this.label1);
            this.GB_1.Controls.Add(this.label6);
            this.GB_1.Controls.Add(this.label7);
            this.GB_1.Controls.Add(this.TB_Code_3);
            this.GB_1.Controls.Add(this.CB_Reject_3);
            this.GB_1.Controls.Add(this.CB_Position_3);
            this.GB_1.Controls.Add(this.TB_Code_2);
            this.GB_1.Controls.Add(this.CB_Reject_2);
            this.GB_1.Controls.Add(this.CB_Position_2);
            this.GB_1.Controls.Add(this.TB_Code_1);
            this.GB_1.Controls.Add(this.CB_Reject_1);
            this.GB_1.Controls.Add(this.CB_Position_1);
            this.GB_1.Location = new System.Drawing.Point(859, 36);
            this.GB_1.Name = "GB_1";
            this.GB_1.Size = new System.Drawing.Size(507, 266);
            this.GB_1.TabIndex = 3;
            this.GB_1.TabStop = false;
            this.GB_1.Text = "不良项";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(352, 56);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(92, 27);
            this.label1.TabIndex = 11;
            this.label1.Text = "不良代码";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(198, 56);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(72, 27);
            this.label6.TabIndex = 10;
            this.label6.Text = "不良项";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(40, 56);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(52, 27);
            this.label7.TabIndex = 9;
            this.label7.Text = "位置";
            // 
            // TB_Code_3
            // 
            this.TB_Code_3.Location = new System.Drawing.Point(357, 202);
            this.TB_Code_3.Name = "TB_Code_3";
            this.TB_Code_3.ReadOnly = true;
            this.TB_Code_3.Size = new System.Drawing.Size(100, 34);
            this.TB_Code_3.TabIndex = 8;
            // 
            // CB_Reject_3
            // 
            this.CB_Reject_3.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CB_Reject_3.FormattingEnabled = true;
            this.CB_Reject_3.Location = new System.Drawing.Point(198, 202);
            this.CB_Reject_3.Name = "CB_Reject_3";
            this.CB_Reject_3.Size = new System.Drawing.Size(121, 35);
            this.CB_Reject_3.TabIndex = 7;
            this.CB_Reject_3.SelectedIndexChanged += new System.EventHandler(this.Reject_IndexChange);
            // 
            // CB_Position_3
            // 
            this.CB_Position_3.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CB_Position_3.FormattingEnabled = true;
            this.CB_Position_3.Location = new System.Drawing.Point(40, 202);
            this.CB_Position_3.Name = "CB_Position_3";
            this.CB_Position_3.Size = new System.Drawing.Size(121, 35);
            this.CB_Position_3.TabIndex = 6;
            this.CB_Position_3.SelectedIndexChanged += new System.EventHandler(this.Position_IndexChange);
            // 
            // TB_Code_2
            // 
            this.TB_Code_2.Location = new System.Drawing.Point(357, 145);
            this.TB_Code_2.Name = "TB_Code_2";
            this.TB_Code_2.ReadOnly = true;
            this.TB_Code_2.Size = new System.Drawing.Size(100, 34);
            this.TB_Code_2.TabIndex = 5;
            // 
            // CB_Reject_2
            // 
            this.CB_Reject_2.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CB_Reject_2.FormattingEnabled = true;
            this.CB_Reject_2.Location = new System.Drawing.Point(198, 145);
            this.CB_Reject_2.Name = "CB_Reject_2";
            this.CB_Reject_2.Size = new System.Drawing.Size(121, 35);
            this.CB_Reject_2.TabIndex = 4;
            this.CB_Reject_2.SelectedIndexChanged += new System.EventHandler(this.Reject_IndexChange);
            // 
            // CB_Position_2
            // 
            this.CB_Position_2.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CB_Position_2.FormattingEnabled = true;
            this.CB_Position_2.Location = new System.Drawing.Point(40, 145);
            this.CB_Position_2.Name = "CB_Position_2";
            this.CB_Position_2.Size = new System.Drawing.Size(121, 35);
            this.CB_Position_2.TabIndex = 3;
            this.CB_Position_2.SelectedIndexChanged += new System.EventHandler(this.Position_IndexChange);
            // 
            // TB_Code_1
            // 
            this.TB_Code_1.Location = new System.Drawing.Point(357, 93);
            this.TB_Code_1.Name = "TB_Code_1";
            this.TB_Code_1.ReadOnly = true;
            this.TB_Code_1.Size = new System.Drawing.Size(100, 34);
            this.TB_Code_1.TabIndex = 2;
            // 
            // CB_Reject_1
            // 
            this.CB_Reject_1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CB_Reject_1.FormattingEnabled = true;
            this.CB_Reject_1.Location = new System.Drawing.Point(198, 93);
            this.CB_Reject_1.Name = "CB_Reject_1";
            this.CB_Reject_1.Size = new System.Drawing.Size(121, 35);
            this.CB_Reject_1.TabIndex = 1;
            this.CB_Reject_1.SelectedIndexChanged += new System.EventHandler(this.Reject_IndexChange);
            // 
            // CB_Position_1
            // 
            this.CB_Position_1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CB_Position_1.FormattingEnabled = true;
            this.CB_Position_1.Location = new System.Drawing.Point(40, 93);
            this.CB_Position_1.Name = "CB_Position_1";
            this.CB_Position_1.Size = new System.Drawing.Size(121, 35);
            this.CB_Position_1.TabIndex = 0;
            this.CB_Position_1.SelectedIndexChanged += new System.EventHandler(this.Position_IndexChange);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.TB_Remark);
            this.groupBox3.Location = new System.Drawing.Point(859, 489);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(507, 160);
            this.groupBox3.TabIndex = 23;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "备注";
            // 
            // TB_Remark
            // 
            this.TB_Remark.Location = new System.Drawing.Point(40, 33);
            this.TB_Remark.Multiline = true;
            this.TB_Remark.Name = "TB_Remark";
            this.TB_Remark.Size = new System.Drawing.Size(433, 104);
            this.TB_Remark.TabIndex = 18;
            this.TB_Remark.TextChanged += new System.EventHandler(this.TB_Remark_TextChanged);
            // 
            // Btn_OK
            // 
            this.Btn_OK.BackColor = System.Drawing.Color.PaleGreen;
            this.Btn_OK.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.Btn_OK.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.Btn_OK.Location = new System.Drawing.Point(1166, 683);
            this.Btn_OK.Name = "Btn_OK";
            this.Btn_OK.Size = new System.Drawing.Size(200, 80);
            this.Btn_OK.TabIndex = 24;
            this.Btn_OK.Text = "通过";
            this.Btn_OK.UseVisualStyleBackColor = false;
            this.Btn_OK.Click += new System.EventHandler(this.Btn_OK_Click);
            // 
            // Btn_NG
            // 
            this.Btn_NG.BackColor = System.Drawing.Color.Pink;
            this.Btn_NG.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.Btn_NG.Location = new System.Drawing.Point(1166, 683);
            this.Btn_NG.Name = "Btn_NG";
            this.Btn_NG.Size = new System.Drawing.Size(200, 80);
            this.Btn_NG.TabIndex = 25;
            this.Btn_NG.Text = "不通过";
            this.Btn_NG.UseVisualStyleBackColor = false;
            this.Btn_NG.Click += new System.EventHandler(this.Btn_NG_Click);
            // 
            // Label_Tip
            // 
            this.Label_Tip.AutoSize = true;
            this.Label_Tip.ForeColor = System.Drawing.Color.Red;
            this.Label_Tip.Location = new System.Drawing.Point(19, 814);
            this.Label_Tip.Name = "Label_Tip";
            this.Label_Tip.Size = new System.Drawing.Size(52, 27);
            this.Label_Tip.TabIndex = 26;
            this.Label_Tip.Text = "提示";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.tb_FixBeam);
            this.groupBox1.Controls.Add(this.radioButton4);
            this.groupBox1.Controls.Add(this.radioButton3);
            this.groupBox1.Controls.Add(this.TB_FixSN);
            this.groupBox1.Location = new System.Drawing.Point(859, 308);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(507, 172);
            this.groupBox1.TabIndex = 27;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "返修流程正常套筒/横梁SN";
            this.groupBox1.Visible = false;
            this.groupBox1.Enter += new System.EventHandler(this.groupBox1_Enter);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(6, 71);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(52, 27);
            this.label9.TabIndex = 15;
            this.label9.Text = "横梁";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(6, 118);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(52, 27);
            this.label8.TabIndex = 14;
            this.label8.Text = "套筒";
            // 
            // tb_FixBeam
            // 
            this.tb_FixBeam.ImeMode = System.Windows.Forms.ImeMode.Disable;
            this.tb_FixBeam.Location = new System.Drawing.Point(62, 68);
            this.tb_FixBeam.Name = "tb_FixBeam";
            this.tb_FixBeam.Size = new System.Drawing.Size(427, 34);
            this.tb_FixBeam.TabIndex = 13;
            this.tb_FixBeam.Visible = false;
            this.tb_FixBeam.Enter += new System.EventHandler(this.TB_FixBeam_Enter);
            this.tb_FixBeam.KeyUp += new System.Windows.Forms.KeyEventHandler(this.TB_FixBeam_KeyUp);
            this.tb_FixBeam.Leave += new System.EventHandler(this.TB_FixBeam_Leave);
            // 
            // radioButton4
            // 
            this.radioButton4.AutoSize = true;
            this.radioButton4.Location = new System.Drawing.Point(240, 33);
            this.radioButton4.Name = "radioButton4";
            this.radioButton4.Size = new System.Drawing.Size(90, 31);
            this.radioButton4.TabIndex = 12;
            this.radioButton4.TabStop = true;
            this.radioButton4.Text = "右套筒";
            this.radioButton4.UseVisualStyleBackColor = true;
            this.radioButton4.CheckedChanged += new System.EventHandler(this.radioButton4_CheckedChanged);
            // 
            // radioButton3
            // 
            this.radioButton3.AutoSize = true;
            this.radioButton3.Location = new System.Drawing.Point(25, 33);
            this.radioButton3.Name = "radioButton3";
            this.radioButton3.Size = new System.Drawing.Size(90, 31);
            this.radioButton3.TabIndex = 11;
            this.radioButton3.TabStop = true;
            this.radioButton3.Text = "左套筒";
            this.radioButton3.UseVisualStyleBackColor = true;
            this.radioButton3.CheckedChanged += new System.EventHandler(this.radioButton3_CheckedChanged);
            // 
            // TB_FixSN
            // 
            this.TB_FixSN.ImeMode = System.Windows.Forms.ImeMode.Disable;
            this.TB_FixSN.Location = new System.Drawing.Point(62, 115);
            this.TB_FixSN.Name = "TB_FixSN";
            this.TB_FixSN.Size = new System.Drawing.Size(427, 34);
            this.TB_FixSN.TabIndex = 10;
            this.TB_FixSN.Visible = false;
            this.TB_FixSN.Enter += new System.EventHandler(this.TB_FixSN_Enter);
            this.TB_FixSN.KeyUp += new System.Windows.Forms.KeyEventHandler(this.TB_FixSN_KeyUp);
            this.TB_FixSN.Leave += new System.EventHandler(this.TB_FixSN_Leave);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.radioButton2);
            this.groupBox2.Controls.Add(this.radioButton1);
            this.groupBox2.Location = new System.Drawing.Point(38, 26);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(680, 65);
            this.groupBox2.TabIndex = 28;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "流程选择";
            this.groupBox2.Enter += new System.EventHandler(this.groupBox2_Enter);
            // 
            // radioButton2
            // 
            this.radioButton2.AutoSize = true;
            this.radioButton2.Location = new System.Drawing.Point(391, 32);
            this.radioButton2.Name = "radioButton2";
            this.radioButton2.Size = new System.Drawing.Size(70, 31);
            this.radioButton2.TabIndex = 1;
            this.radioButton2.TabStop = true;
            this.radioButton2.Text = "返修";
            this.radioButton2.UseVisualStyleBackColor = true;
            this.radioButton2.CheckedChanged += new System.EventHandler(this.radioButton2_CheckedChanged);
            // 
            // radioButton1
            // 
            this.radioButton1.AutoSize = true;
            this.radioButton1.Checked = true;
            this.radioButton1.Location = new System.Drawing.Point(179, 32);
            this.radioButton1.Name = "radioButton1";
            this.radioButton1.Size = new System.Drawing.Size(70, 31);
            this.radioButton1.TabIndex = 0;
            this.radioButton1.TabStop = true;
            this.radioButton1.Text = "正常";
            this.radioButton1.UseVisualStyleBackColor = true;
            this.radioButton1.CheckedChanged += new System.EventHandler(this.radioButton1_CheckedChanged);
            // 
            // menuStrip1
            // 
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.菜单ToolStripMenuItem});
            this.menuStrip1.Location = new System.Drawing.Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Size = new System.Drawing.Size(1396, 25);
            this.menuStrip1.TabIndex = 29;
            this.menuStrip1.Text = "menuStrip1";
            // 
            // 菜单ToolStripMenuItem
            // 
            this.菜单ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.参数配置ToolStripMenuItem});
            this.菜单ToolStripMenuItem.Name = "菜单ToolStripMenuItem";
            this.菜单ToolStripMenuItem.Size = new System.Drawing.Size(44, 21);
            this.菜单ToolStripMenuItem.Text = "菜单";
            // 
            // 参数配置ToolStripMenuItem
            // 
            this.参数配置ToolStripMenuItem.Name = "参数配置ToolStripMenuItem";
            this.参数配置ToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.参数配置ToolStripMenuItem.Text = "参数配置";
            this.参数配置ToolStripMenuItem.Click += new System.EventHandler(this.ConfigItem_Click);
            // 
            // FormMain
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(1396, 850);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.Label_Tip);
            this.Controls.Add(this.Btn_NG);
            this.Controls.Add(this.Btn_OK);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.GB_1);
            this.Controls.Add(this.Group_Sn);
            this.Controls.Add(this.menuStrip1);
            this.Font = new System.Drawing.Font("微软雅黑", 15F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MainMenuStrip = this.menuStrip1;
            this.Margin = new System.Windows.Forms.Padding(6, 7, 6, 7);
            this.Name = "FormMain";
            this.Text = "Xreal双目预检工具";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormMain_FormClosing);
            this.Load += new System.EventHandler(this.FormMain_Load);
            this.Group_Sn.ResumeLayout(false);
            this.Group_Sn.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.GB_1.ResumeLayout(false);
            this.GB_1.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private void ResizeForm()
        {
            this.Scale(new System.Drawing.SizeF(1.25f, 1.25f));
        }

        protected override void OnLoad(EventArgs e)
        {

            base.OnLoad(e);
            //ResizeForm();
        }
        #endregion

        private System.Windows.Forms.GroupBox Group_Sn;
        private System.Windows.Forms.TextBox TB_RightOledSn;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox TB_RightBracketSn;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox TB_LeftOledSn;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox TB_LeftBracketSn;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label Label_BeamSn;
        private System.Windows.Forms.GroupBox GB_1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox TB_Code_3;
        private System.Windows.Forms.ComboBox CB_Reject_3;
        private System.Windows.Forms.ComboBox CB_Position_3;
        private System.Windows.Forms.TextBox TB_Code_2;
        private System.Windows.Forms.ComboBox CB_Reject_2;
        private System.Windows.Forms.ComboBox CB_Position_2;
        private System.Windows.Forms.TextBox TB_Code_1;
        private System.Windows.Forms.ComboBox CB_Reject_1;
        private System.Windows.Forms.ComboBox CB_Position_1;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.TextBox TB_Remark;
        private System.Windows.Forms.Button Btn_OK;
        private System.Windows.Forms.Button Btn_NG;
        private System.Windows.Forms.Button Btn_CleanRight;
        private System.Windows.Forms.Button Btn_CleanLeft;
        private System.Windows.Forms.Label Label_Tip;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.TextBox TB_FixSN;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.RadioButton radioButton2;
        private System.Windows.Forms.RadioButton radioButton1;
        private System.Windows.Forms.Label beam_sn_label;
        private System.Windows.Forms.RadioButton radioButton3;
        private System.Windows.Forms.RadioButton radioButton4;
        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.DataGridViewTextBoxColumn mtf;
        private System.Windows.Forms.DataGridViewTextBoxColumn gray;
        private System.Windows.Forms.DataGridViewTextBoxColumn angle;
        private System.Windows.Forms.DataGridViewTextBoxColumn lr_magrate;
        private System.Windows.Forms.DataGridViewTextBoxColumn tb_magrate;
        private System.Windows.Forms.DataGridViewTextBoxColumn center_x;
        private System.Windows.Forms.DataGridViewTextBoxColumn center_y;
        private System.Windows.Forms.DataGridViewTextBoxColumn point0_x;
        private System.Windows.Forms.DataGridViewTextBoxColumn point0_y;
        private System.Windows.Forms.DataGridViewTextBoxColumn point4_x;
        private System.Windows.Forms.DataGridViewTextBoxColumn point4_y;
        private System.Windows.Forms.DataGridViewTextBoxColumn point20_x;
        private System.Windows.Forms.DataGridViewTextBoxColumn point20_y;
        private System.Windows.Forms.DataGridViewTextBoxColumn point24_x;
        private System.Windows.Forms.DataGridViewTextBoxColumn point24_y;
        private System.Windows.Forms.MenuStrip menuStrip1;
        private System.Windows.Forms.ToolStripMenuItem 菜单ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 参数配置ToolStripMenuItem;
        private System.Windows.Forms.TextBox tb_FixBeam;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label8;
    }
}

