using ParamManager;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using ParamManager;
using static System.Net.Mime.MediaTypeNames;
using System.Web.SessionState;

namespace DoubleCamPreliminary
{
    public class DataCache
    {
        public static string filePath;

        public static string project;
        //数据库
        public static string dbIP;
        public static string dbPort;
        public static string dbUserName;
        public static string dbPassword;
        public static string dbDataBase;

        public static bool beamSnCheck;
        public static bool diopterCheck;
        public static bool pointCheck;
        public static string station;
        public static int beamSnLength;
        public static int bracketSnLength;
        public static int oledSnLength;

        //校验数据
        public static double mtfDifferMax;
        public static double mtfDifferMin;
        public static double grayDifferMax;
        public static double grayDifferMin;
        public static double angle2DifferMax;
        public static double angle2DifferMin;
        public static double diopterDifferMax;
        public static double diopterDifferMin;
        public static double magLRDifferMin;
        public static double magLRDifferMax;
        public static double magTBDifferMin;
        public static double magTBDifferMax;

        public static double centerXDifferMin;
        public static double centerXDifferMax;
        public static double centerYDifferMin;
        public static double centerYDifferMax;
        public static double point0XDifferMin;
        public static double point0XDifferMax;
        public static double point0YDifferMin;
        public static double point0YDifferMax;
        public static double point4XDifferMin;
        public static double point4XDifferMax;
        public static double point4YDifferMin;
        public static double point4YDifferMax;
        public static double point20XDifferMin;
        public static double point20XDifferMax;
        public static double point20YDifferMin;
        public static double point20YDifferMax;
        public static double point24XDifferMin;
        public static double point24XDifferMax;
        public static double point24YDifferMin;
        public static double point24YDifferMax;

        //运行数据
        public static bool diopterDiffer;
        public static bool angle2Differ;
        public static  bool mtfDiffer;
        public static  bool grayDiffer;
        public static  string finalResult;
        public static bool magLRDiffer;
        public static bool magTBDiffer;

        public static bool centerX_Differ=false;
        public static bool centerY_Differ = false;
        public static bool point0X_Differ = false;
        public static bool point0Y_Differ = false;
        public static bool point4X_Differ = false;
        public static bool point4Y_Differ = false;
        public static bool point20X_Differ = false;
        public static bool point20Y_Differ = false;
        public static bool point24X_Differ = false;
        public static bool point24Y_Differ = false;

        public static string MesResource;

        public static PM pm;
        public static void Init(string filePath)
        {
            //filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "sys.ini");

            station = Read("Sys", "station", "1", filePath);

            beamSnLength = Convert.ToInt32(Read("Check", "snBeamLength", "1", filePath));
            bracketSnLength = Convert.ToInt32(Read("Check", "snBracketLength", "1", filePath));
            oledSnLength = Convert.ToInt32(Read("Check", "snOledLength", "1", filePath));

            dbIP = Read("MySql", "IP", "1", filePath);
            dbPort = Read("MySql", "Port", "1", filePath);
            dbUserName = Read("MySql", "UserName", "1", filePath);
            dbPassword = Read("MySql", "Password", "1", filePath);
            dbDataBase = Read("MySql", "DataBase", "1", filePath);

            mtfDifferMax = Convert.ToDouble(Read("Verify", "mtfDifferMax", "1", filePath));
            mtfDifferMin = Convert.ToDouble(Read("Verify", "mtfDifferMin", "1", filePath));
            grayDifferMax = Convert.ToDouble(Read("Verify", "grayDifferMax", "1", filePath));
            grayDifferMin = Convert.ToDouble(Read("Verify", "grayDifferMin", "1", filePath));
            angle2DifferMax = Convert.ToDouble(Read("Verify", "angle2DifferMax", "1", filePath));
            angle2DifferMin = Convert.ToDouble(Read("Verify", "angle2DifferMin", "1", filePath));
            diopterDifferMax = Convert.ToDouble(Read("Verify", "diopterDifferMax", "1", filePath));
            diopterDifferMin = Convert.ToDouble(Read("Verify", "diopterDifferMin", "1", filePath));
            magLRDifferMin = Convert.ToDouble(Read("Verify", "magLRDifferMin", "1", filePath));
            magLRDifferMax = Convert.ToDouble(Read("Verify", "magLRDifferMax", "1", filePath));
            magTBDifferMin = Convert.ToDouble(Read("Verify", "magTBDifferMin", "1", filePath));
            magTBDifferMax = Convert.ToDouble(Read("Verify", "magTBDifferMax", "1", filePath));

            centerXDifferMin = Convert.ToDouble(Read("Verify", "centerXDifferMin", "1", filePath));
            centerXDifferMax = Convert.ToDouble(Read("Verify", "centerXDifferMax", "1", filePath));
            centerYDifferMin = Convert.ToDouble(Read("Verify", "centerYDifferMin", "1", filePath));
            centerYDifferMax = Convert.ToDouble(Read("Verify", "centerYDifferMax", "1", filePath));
            point0XDifferMin = Convert.ToDouble(Read("Verify", "point0XDifferMin", "1", filePath));
            point0XDifferMax = Convert.ToDouble(Read("Verify", "point0XDifferMax", "1", filePath));
            point0YDifferMin = Convert.ToDouble(Read("Verify", "point0YDifferMin", "1", filePath));
            point0YDifferMax = Convert.ToDouble(Read("Verify", "point0YDifferMax", "1", filePath));
            point4XDifferMin = Convert.ToDouble(Read("Verify", "point4XDifferMin", "1", filePath));
            point4XDifferMax = Convert.ToDouble(Read("Verify", "point4XDifferMax", "1", filePath));
            point4YDifferMin = Convert.ToDouble(Read("Verify", "point4YDifferMin", "1", filePath));
            point4YDifferMax = Convert.ToDouble(Read("Verify", "point4YDifferMax", "1", filePath));
            point20XDifferMin = Convert.ToDouble(Read("Verify", "point20XDifferMin", "1", filePath));
            point20XDifferMax = Convert.ToDouble(Read("Verify", "point20XDifferMax", "1", filePath));
            point20YDifferMin = Convert.ToDouble(Read("Verify", "point20YDifferMin", "1", filePath));
            point20YDifferMax = Convert.ToDouble(Read("Verify", "point20YDifferMax", "1", filePath));
            point24XDifferMin = Convert.ToDouble(Read("Verify", "point24XDifferMin", "1", filePath));
            point24XDifferMax = Convert.ToDouble(Read("Verify", "point24XDifferMax", "1", filePath));
            point24YDifferMin = Convert.ToDouble(Read("Verify", "point24YDifferMin", "1", filePath));
            point24YDifferMax = Convert.ToDouble(Read("Verify", "point24YDifferMax", "1", filePath));


            beamSnCheck = Convert.ToBoolean(Read("Verify", "beamSnCheck", "false", filePath));
            diopterCheck = Convert.ToBoolean(Read("Verify", "diopterCheck", "false", filePath));
        }

        public static void InitNew(bool flag)
        {
            pm = new PM();
            pm.updateStationData += UpdateStationDataFunc;

            pm.InitAll(flag, 26, 0);
            pm.readPosition = 0;

            Para_get(ref pm);

        }

        public static void openSetting()
        {
            pm.ShowForm("SingleStation", "");
        }

        public static int Init_Local()
        {
            pm = new PM();
            pm.updateStationData += UpdateStationDataFunc;
            int res = pm.ReadConfigFile();
            if (res != 0)
                return res;

            Para_get(ref pm);

            return 0;
        }

        private static void UpdateStationDataFunc(string stationNo, string projectName, string mode)
        {
            project = projectName;
        }


        public static void Para_get(ref PM pm)
        {
            beamSnLength = pm.Vi("Check#beamSnLength");
            bracketSnLength = pm.Vi("Check#snBracketLength");
            oledSnLength = pm.Vi("Check#snOledLength");
            mtfDifferMax = pm.Vd("Verify#mtfDifferMax");
            mtfDifferMin = pm.Vd("Verify#mtfDifferMin");
            grayDifferMax = pm.Vd("Verify#grayDifferMax");
            grayDifferMin = pm.Vd("Verify#grayDifferMin");
            angle2DifferMax = pm.Vd("Verify#angle2DifferMax");
            angle2DifferMin = pm.Vd("Verify#angle2DifferMin");
            diopterDifferMax = pm.Vd("Verify#diopterDifferMax");
            diopterDifferMin = pm.Vd("Verify#diopterDifferMin");
            magLRDifferMin = pm.Vd("Verify#magLRDifferMin");
            magLRDifferMax = pm.Vd("Verify#magLRDifferMax");
            magTBDifferMin = pm.Vd("Verify#magTBDifferMin");
            magTBDifferMax = pm.Vd("Verify#magTBDifferMax");

            centerXDifferMin = pm.Vd("Verify#centerXDifferMin");
            centerXDifferMax = pm.Vd("Verify#centerXDifferMax");
            centerYDifferMin = pm.Vd("Verify#centerYDifferMin");
            centerYDifferMax = pm.Vd("Verify#centerYDifferMax");
            point0XDifferMin = pm.Vd("Verify#point0XDifferMin");
            point0XDifferMax = pm.Vd("Verify#point0XDifferMax");
            point0YDifferMin = pm.Vd("Verify#point0YDifferMin");
            point0YDifferMax = pm.Vd("Verify#point0YDifferMax");
            point4XDifferMin = pm.Vd("Verify#point4XDifferMin");
            point4XDifferMax = pm.Vd("Verify#point4XDifferMax");
            point4YDifferMin = pm.Vd("Verify#point4YDifferMin");
            point4YDifferMax = pm.Vd("Verify#point4YDifferMax");
            point20XDifferMin = pm.Vd("Verify#point20XDifferMin");
            point20XDifferMax = pm.Vd("Verify#point20XDifferMax");
            point20YDifferMin = pm.Vd("Verify#point20YDifferMin");
            point20YDifferMax = pm.Vd("Verify#point20YDifferMax");
            point24XDifferMin = pm.Vd("Verify#point24XDifferMin");
            point24XDifferMax = pm.Vd("Verify#point24XDifferMax");
            point24YDifferMin = pm.Vd("Verify#point24YDifferMin");
            point24YDifferMax = pm.Vd("Verify#point24YDifferMax");

            beamSnCheck = pm.Vb("Check#beamSnCheck");
            diopterCheck = pm.Vb("Check#diopterCheck");
            pointCheck = pm.Vb("Check#pointCheck");
            MesResource = pm.Vs("Check#MesResource");

            Logs.WriteDebug("param Check#beamSnLength " + beamSnLength.ToString(), true);
            Logs.WriteDebug("param Check#snBracketLength " + bracketSnLength.ToString(), true);
            Logs.WriteDebug("param Check#snOledLength " + oledSnLength.ToString(), true);
            Logs.WriteDebug("param Verify#mtfDifferMax " + mtfDifferMax.ToString(), true);
            Logs.WriteDebug("param Verify#mtfDifferMin " + mtfDifferMin.ToString(), true);
            Logs.WriteDebug("param Verify#grayDifferMax " + grayDifferMax.ToString(), true);
            Logs.WriteDebug("param Verify#grayDifferMin " + grayDifferMin.ToString(), true);
            Logs.WriteDebug("param Verify#angle2DifferMax " + angle2DifferMax.ToString(), true);
            Logs.WriteDebug("param Verify#angle2DifferMin " + angle2DifferMin.ToString(), true);
            Logs.WriteDebug("param Verify#diopterDifferMax " + diopterDifferMax.ToString(), true);
            Logs.WriteDebug("param Verify#diopterDifferMin " + diopterDifferMin.ToString(), true);
            Logs.WriteDebug("param Check#beamSnCheck " + beamSnCheck.ToString(), true);
            Logs.WriteDebug("param Check#diopterCheck " + diopterCheck.ToString(), true);
            Logs.WriteDebug("param Check#pointCheck " + pointCheck.ToString(), true);
            Logs.WriteDebug("param Check#MesResource " + MesResource.ToString(), true);
        }


        [DllImport("kernel32")]
        public static extern int GetPrivateProfileString(string lpAppName, string lpKeyName, string lpDefault, StringBuilder lpReturnedString, int nSize, string lpFileName);

        [DllImport("kernel32")]
        public static extern int WritePrivateProfileString(string lpApplicationName, string lpKeyName, string lpString, string lpFileName);

        public static string Read(string section, string key, string def, string filePath)
        {
            StringBuilder sb = new StringBuilder(1024);
            GetPrivateProfileString(section, key, def, sb, 1024, filePath);
            return sb.ToString();
        }

        public static int Write(string section, string key, string value, string filePath)
        {
            return WritePrivateProfileString(section, key, value, filePath);
        }

        public static void CheckPoint(
            bool isPointCheckEnabled,
            Dictionary<string, double> leftPoints,
            Dictionary<string, double> rightPoints,
            Action<string, double, double, double, double> updateGridCallback)
        {
            if (!isPointCheckEnabled)
            {
                centerX_Differ = true;
                centerY_Differ = true;
                point0X_Differ = true;
                point0Y_Differ = true;
                point4X_Differ = true;
                point4Y_Differ = true;
                point20X_Differ = true;
                point20Y_Differ = true;
                point24X_Differ = true;
                point24Y_Differ = true;
                Logs.WriteDebug("Point check is disabled, skipping point verification", true);
                return;
            }

            if (leftPoints == null || rightPoints == null)
            {
                centerX_Differ = false;
                centerY_Differ = false;
                point0X_Differ = false;
                point0Y_Differ = false;
                point4X_Differ = false;
                point4Y_Differ = false;
                point20X_Differ = false;
                point20Y_Differ = false;
                point24X_Differ = false;
                point24Y_Differ = false;
                return;
            }

            // Check Center X
            double Left_centerX = leftPoints["centerX"];
            double Right_centerX = rightPoints["centerX"];
            updateGridCallback("Center_X", Left_centerX, Right_centerX, centerXDifferMin, centerXDifferMax);
            double differenceCenterX = Left_centerX - Right_centerX;
            if (differenceCenterX > centerXDifferMin && differenceCenterX < centerXDifferMax)
            {
                Logs.WriteDebug("左右目centerX差:" + differenceCenterX.ToString(), true);
                centerX_Differ = true;
            }
            else
            {
                Logs.WriteDebug("左右目centerX差异常:" + differenceCenterX.ToString(), true);
                centerX_Differ = false;
            }

            // Check Center Y
            double Left_centerY = leftPoints["centerY"];
            double Right_centerY = rightPoints["centerY"];
            updateGridCallback("Center_Y", Left_centerY, Right_centerY, centerYDifferMin, centerYDifferMax);
            double differenceCenterY = Left_centerY - Right_centerY;
            if (differenceCenterY > centerYDifferMin && differenceCenterY < centerYDifferMax)
            {
                Logs.WriteDebug("左右目centerY差:" + differenceCenterY.ToString(), true);
                centerY_Differ = true;
            }
            else
            {
                Logs.WriteDebug("左右目centerY差异常:" + differenceCenterY.ToString(), true);
                centerY_Differ = false;
            }

            // Check Point0 X
            double Left_point0X = leftPoints["point0X"];
            double Right_point0X = rightPoints["point0X"];
            updateGridCallback("Point0_X", Left_point0X, Right_point0X, point0XDifferMin, point0XDifferMax);
            double differencePoint0X = Left_point0X - Right_point0X;
            if (differencePoint0X > point0XDifferMin && differencePoint0X < point0XDifferMax)
            {
                Logs.WriteDebug("左右目point0X差:" + differencePoint0X.ToString(), true);
                point0X_Differ = true;
            }
            else
            {
                Logs.WriteDebug("左右目point0X差异常:" + differencePoint0X.ToString(), true);
                point0X_Differ = false;
            }

            // Check Point0 Y
            double Left_point0Y = leftPoints["point0Y"];
            double Right_point0Y = rightPoints["point0Y"];
            updateGridCallback("Point0_Y", Left_point0Y, Right_point0Y, point0YDifferMin, point0YDifferMax);
            double differencePoint0Y = Left_point0Y - Right_point0Y;
            if (differencePoint0Y > point0YDifferMin && differencePoint0Y < point0YDifferMax)
            {
                Logs.WriteDebug("左右目point0Y差:" + differencePoint0Y.ToString(), true);
                point0Y_Differ = true;
            }
            else
            {
                Logs.WriteDebug("左右目point0Y差异常:" + differencePoint0Y.ToString(), true);
                point0Y_Differ = false;
            }

            // Check Point4 X
            double Left_point4X = leftPoints["point4X"];
            double Right_point4X = rightPoints["point4X"];
            updateGridCallback("Point4_X", Left_point4X, Right_point4X, point4XDifferMin, point4XDifferMax);
            double differencePoint4X = Left_point4X - Right_point4X;
            point4X_Differ = differencePoint4X > point4XDifferMin && differencePoint4X < point4XDifferMax;
            Logs.WriteDebug("左右目point4X差:" + (point4X_Differ ? "" : "异常:") + differencePoint4X.ToString(), true);

            // Check Point4 Y
            double Left_point4Y = leftPoints["point4Y"];
            double Right_point4Y = rightPoints["point4Y"];
            updateGridCallback("Point4_Y", Left_point4Y, Right_point4Y, point4YDifferMin, point4YDifferMax);
            double differencePoint4Y = Left_point4Y - Right_point4Y;
            point4Y_Differ = differencePoint4Y > point4YDifferMin && differencePoint4Y < point4YDifferMax;
            Logs.WriteDebug("左右目point4Y差:" + (point4Y_Differ ? "" : "异常:") + differencePoint4Y.ToString(), true);

            // Check Point20 X
            double Left_point20X = leftPoints["point20X"];
            double Right_point20X = rightPoints["point20X"];
            updateGridCallback("Point20_X", Left_point20X, Right_point20X, point20XDifferMin, point20XDifferMax);
            double differencePoint20X = Left_point20X - Right_point20X;
            point20X_Differ = differencePoint20X > point20XDifferMin && differencePoint20X < point20XDifferMax;
            Logs.WriteDebug("左右目point20X差:" + (point20X_Differ ? "" : "异常:") + differencePoint20X.ToString(), true);

            // Check Point20 Y
            double Left_point20Y = leftPoints["point20Y"];
            double Right_point20Y = rightPoints["point20Y"];
            updateGridCallback("Point20_Y", Left_point20Y, Right_point20Y, point20YDifferMin, point20YDifferMax);
            double differencePoint20Y = Left_point20Y - Right_point20Y;
            point20Y_Differ = differencePoint20Y > point20YDifferMin && differencePoint20Y < point20YDifferMax;
            Logs.WriteDebug("左右目point20Y差:" + (point20Y_Differ ? "" : "异常:") + differencePoint20Y.ToString(), true);

            // Check Point24 X
            double Left_point24X = leftPoints["point24X"];
            double Right_point24X = rightPoints["point24X"];
            updateGridCallback("Point24_X", Left_point24X, Right_point24X, point24XDifferMin, point24XDifferMax);
            double differencePoint24X = Left_point24X - Right_point24X;
            point24X_Differ = differencePoint24X > point24XDifferMin && differencePoint24X < point24XDifferMax;
            Logs.WriteDebug("左右目point24X差:" + (point24X_Differ ? "" : "异常:") + differencePoint24X.ToString(), true);

            // Check Point24 Y
            double Left_point24Y = leftPoints["point24Y"];
            double Right_point24Y = rightPoints["point24Y"];
            updateGridCallback("Point24_Y", Left_point24Y, Right_point24Y, point24YDifferMin, point24YDifferMax);
            double differencePoint24Y = Left_point24Y - Right_point24Y;
            point24Y_Differ = differencePoint24Y > point24YDifferMin && differencePoint24Y < point24YDifferMax;
            Logs.WriteDebug("左右目point24Y差:" + (point24Y_Differ ? "" : "异常:") + differencePoint24Y.ToString(), true);
        }

    }
}
