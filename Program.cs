using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace DoubleCamPreliminary
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {

            if (args.Length == 10)
            {
                LOGINFO.mode = args[0];
                LOGINFO.user = args[1];
                LOGINFO.rname = args[2];
                LOGINFO.lname = args[3];
                LOGINFO.pname = args[4];
                LOGINFO.configMode = args[5];
                LOGINFO.dbWrite = args[6];
                LOGINFO.mesEnable = args[7];
                LOGINFO.pass = args[8];
            }
            if (args.Length == 0)
            {
                LOGINFO.user = "test";
                LOGINFO.mode = "offline";
                LOGINFO.configMode = "local";
                LOGINFO.lname = "N1_1";
                LOGINFO.dbWrite = "false";
                LOGINFO.mesEnable = "false";
                LOGINFO.pass = "123456";
            }

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new FormMain());
        }
    }


    public class LOGINFO
    {
        public static string mode = "";
        public static string token = "default";
        public static string user = "";
        public static string rname = "default";
        public static string lname = "default";
        public static string pname = "default";
        public static string configMode = "net";  // net local
        public static string dbWrite = "true";
        public static string mesEnable = "true";
        public static string pass = "";
        public static string reserved2 = "";
        public static string scan = "true";
    }

    public class Context
    {
        public string SN { get; set; }
        public string S_SN { get; set; }
        public double GWeight { get; set; }
    }
}
